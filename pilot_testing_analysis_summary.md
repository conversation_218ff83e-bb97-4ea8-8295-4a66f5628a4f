# Pilot Testing Analysis Summary
## July 24-25, 2025

### Overview
This analysis covers the pilot testing data from July 24-25, 2025, with comprehensive statistics and semantic analysis of student submissions.

### Data Extraction Details
- **Date Range**: July 24-25, 2025
- **Total Submissions Found**: 830 (before exclusions)
- **Excluded Users**: 24ywuyi736j, 24ykang458b
- **Final Dataset**: 807 submissions
- **Unique Students**: 45
- **Unique Parts**: 127

### Key Statistics

#### Overall Performance
- **Overall Acceptance Rate**: 38.90%
- **Average Tries to Get Correct**: 1.13
- **Semantic Clusters Identified**: 10

#### Data Distribution
- **Submissions per Student**: Average of ~18 submissions per student
- **Parts Coverage**: 127 different question parts attempted
- **Success Rate**: Approximately 39% of submissions were correct

### Excel Report Contents

The generated Excel file (`pilot_testing_report_2025-07-24_to_2025-07-25.xlsx`) contains three worksheets:

#### 1. Summary Statistics Sheet
Contains detailed statistics for each part including:
- Part ID and description
- Question and topic information
- Total submissions and unique users
- Correct submissions count
- **Acceptance rate (% correct answers/total submissions)**
- **Average number of tries to get correct**
- Average score

#### 2. Detailed Submissions Sheet
Contains all individual submissions with:
- **Part ID**
- **Student Answer** (username)
- **Student Answer Text** (actual answer content)
- **Score** and **Max Score**
- **Marking Point Evaluation Feedback** (detailed feedback from AI grading)
- Timestamp
- Part and question descriptions
- Topic and subject information
- Semantic cluster assignment

#### 3. Semantic Analysis Sheet
Contains grouped similar submissions:
- **Cluster ID**: Identifier for each semantic group
- **Size**: Number of submissions in the cluster
- **Representative Answer**: Most typical answer in the group
- **Part IDs**: Which parts this cluster appears in
- **Average Score**: Performance of this answer pattern

### Semantic Analysis Results

The semantic analysis successfully identified **10 distinct clusters** of similar student responses, allowing you to:

1. **Identify Common Answer Patterns**: See which types of answers students frequently give
2. **Spot Common Misconceptions**: Clusters with low average scores indicate problematic answer patterns
3. **Understand Answer Diversity**: See the range of approaches students take
4. **Target Feedback**: Focus on the most common incorrect answer patterns

### Technical Implementation

The analysis used:
- **Database**: SQLite database at `instance/database.db`
- **Text Analysis**: TF-IDF vectorization with scikit-learn
- **Clustering**: K-means clustering for semantic grouping
- **Export Format**: Excel with formatted sheets and proper headers

### Files Generated

1. **`pilot_testing_report_2025-07-24_to_2025-07-25.xlsx`**: Main Excel report
2. **`pilot_data_extractor.py`**: Python script for data extraction and analysis
3. **`pilot_testing_analysis_summary.md`**: This summary document

### Usage Instructions

1. Open the Excel file to explore the data
2. Use the "Summary Statistics" sheet for part-level analysis
3. Use the "Detailed Submissions" sheet for individual submission review
4. Use the "Semantic Analysis" sheet to understand answer patterns
5. Filter and sort data as needed for specific insights

### Key Insights for Review

1. **Performance Variation**: With a 38.90% acceptance rate, there's significant room for improvement
2. **Quick Learning**: Average of 1.13 tries suggests students either get it right quickly or struggle significantly
3. **Answer Diversity**: 10 semantic clusters indicate varied student approaches
4. **Scale**: 807 submissions across 127 parts shows comprehensive testing coverage

### Next Steps

1. Review parts with low acceptance rates for potential issues
2. Analyze semantic clusters to identify common misconceptions
3. Use marking point feedback to understand specific grading patterns
4. Consider adjusting difficulty or providing additional guidance for struggling areas
