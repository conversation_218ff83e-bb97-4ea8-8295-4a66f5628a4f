#!/usr/bin/env python3
"""
Pilot Testing Data Extractor and Analyzer
Extracts submission data for pilot testing analysis on July 24-25, 2025
Creates detailed Excel report with statistics and semantic analysis
"""

import sqlite3
import pandas as pd
import json
from datetime import datetime, date
import os
import sys
import numpy as np
from collections import defaultdict
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from sklearn.metrics.pairwise import cosine_similarity
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

def connect_to_database():
    """Connect to the SQLite database"""
    db_path = 'instance/database.db'

    if os.path.exists(db_path):
        print(f"Found database at: {db_path}")
        return sqlite3.connect(db_path)

    print("Database not found!")
    return None

def get_submissions_for_dates(conn, start_date, end_date, excluded_users=None):
    """
    Extract submissions for specified date range

    Args:
        conn: Database connection
        start_date: Start date (YYYY-MM-DD format)
        end_date: End date (YYYY-MM-DD format)
        excluded_users: List of user IDs or usernames to exclude
    """

    # First, let's examine the database structure
    cursor = conn.cursor()

    # Query submissions for the date range with all necessary joins
    query = """
    SELECT
        s.id as submission_id,
        s.user_id,
        u.username,
        s.question_id,
        s.part_id,
        s.answer,
        s.score,
        s.feedback,
        s.timestamp,
        p.description as part_description,
        p.score as max_score,
        p.input_type,
        q.description as question_description,
        t.name as topic_name,
        sub.name as subject_name
    FROM submissions s
    JOIN users u ON s.user_id = u.id
    JOIN parts p ON s.part_id = p.id
    JOIN questions q ON s.question_id = q.id
    JOIN topics t ON q.topic_id = t.id
    JOIN subjects sub ON t.subject_id = sub.id
    WHERE DATE(s.timestamp) BETWEEN ? AND ?
    ORDER BY s.part_id, s.user_id, s.timestamp
    """

    cursor.execute(query, (start_date, end_date))
    results = cursor.fetchall()

    print(f"\nFound {len(results)} submissions between {start_date} and {end_date}")

    # Convert to DataFrame for easier analysis
    columns = [
        'submission_id', 'user_id', 'username', 'question_id', 'part_id',
        'answer', 'score', 'feedback', 'timestamp', 'part_description',
        'max_score', 'input_type', 'question_description', 'topic_name', 'subject_name'
    ]

    df = pd.DataFrame(results, columns=columns)

    # Filter out excluded users if specified
    if excluded_users:
        print(f"Excluding users: {excluded_users}")
        df = df[~df['username'].isin(excluded_users)]
        print(f"After exclusions: {len(df)} submissions")

    return df

def parse_feedback_json(feedback_str):
    """Parse the feedback JSON string to extract evaluation details"""
    if not feedback_str:
        return {}

    try:
        feedback_data = json.loads(feedback_str)
        return feedback_data
    except (json.JSONDecodeError, TypeError):
        return {}

def calculate_part_statistics(df):
    """Calculate detailed statistics for each part"""

    part_stats = []

    for part_id in df['part_id'].unique():
        part_data = df[df['part_id'] == part_id].copy()
        part_data = part_data.sort_values(['user_id', 'timestamp'])

        # Basic info
        part_desc = part_data['part_description'].iloc[0]
        max_score = part_data['max_score'].iloc[0]
        input_type = part_data['input_type'].iloc[0]
        question_desc = part_data['question_description'].iloc[0]
        topic_name = part_data['topic_name'].iloc[0]
        subject_name = part_data['subject_name'].iloc[0]

        # Calculate statistics
        total_submissions = len(part_data)
        unique_users = part_data['user_id'].nunique()

        # Calculate correct answers (assuming score == max_score means correct)
        correct_submissions = len(part_data[part_data['score'] == max_score])
        acceptance_rate = (correct_submissions / total_submissions * 100) if total_submissions > 0 else 0

        # Calculate average tries to get correct for each user
        user_tries = []
        for user_id in part_data['user_id'].unique():
            user_submissions = part_data[part_data['user_id'] == user_id].sort_values('timestamp')

            # Find first correct submission
            correct_idx = None
            for idx, (_, row) in enumerate(user_submissions.iterrows()):
                if row['score'] == max_score:
                    correct_idx = idx
                    break

            if correct_idx is not None:
                tries_to_correct = correct_idx + 1  # +1 because index is 0-based
                user_tries.append(tries_to_correct)

        avg_tries_to_correct = np.mean(user_tries) if user_tries else 0

        # Average score
        avg_score = part_data['score'].mean()

        part_stats.append({
            'part_id': part_id,
            'part_description': part_desc,
            'question_description': question_desc,
            'topic_name': topic_name,
            'subject_name': subject_name,
            'input_type': input_type,
            'max_score': max_score,
            'total_submissions': total_submissions,
            'unique_users': unique_users,
            'correct_submissions': correct_submissions,
            'acceptance_rate': acceptance_rate,
            'avg_tries_to_correct': avg_tries_to_correct,
            'avg_score': avg_score
        })

    return pd.DataFrame(part_stats)

def perform_semantic_analysis(df):
    """Group similar submissions using semantic analysis"""

    # Prepare text data for analysis
    df['clean_answer'] = df['answer'].astype(str).str.lower().str.strip()

    # Remove very short answers (likely not meaningful)
    meaningful_answers = df[df['clean_answer'].str.len() > 5].copy()

    if len(meaningful_answers) == 0:
        return df, []

    # Use TF-IDF to vectorize answers
    vectorizer = TfidfVectorizer(
        max_features=1000,
        stop_words='english',
        ngram_range=(1, 2),
        min_df=2
    )

    try:
        tfidf_matrix = vectorizer.fit_transform(meaningful_answers['clean_answer'])

        # Check if we have enough data for clustering
        if tfidf_matrix.shape[0] < 4:
            print("Not enough meaningful answers for clustering")
            df['semantic_cluster'] = -1
            return df, []

        # Determine optimal number of clusters (max 10, min 2)
        n_clusters = min(10, max(2, tfidf_matrix.shape[0] // 5))

        # Perform clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        clusters = kmeans.fit_predict(tfidf_matrix)

        # Add cluster labels to dataframe
        meaningful_answers['semantic_cluster'] = clusters

        # Merge back with original dataframe
        df = df.merge(
            meaningful_answers[['submission_id', 'semantic_cluster']],
            on='submission_id',
            how='left'
        )
        df['semantic_cluster'] = df['semantic_cluster'].fillna(-1)  # -1 for unclustered

        # Analyze clusters
        cluster_analysis = []
        for cluster_id in range(n_clusters):
            cluster_data = meaningful_answers[meaningful_answers['semantic_cluster'] == cluster_id]

            if len(cluster_data) == 0:
                continue

            # Get representative answers (closest to centroid)
            cluster_mask = meaningful_answers['semantic_cluster'] == cluster_id
            cluster_indices = meaningful_answers[cluster_mask].index.tolist()

            # Get the rows from tfidf_matrix corresponding to this cluster
            original_indices = meaningful_answers.index.tolist()
            tfidf_cluster_indices = [original_indices.index(idx) for idx in cluster_indices if idx in original_indices]

            if len(tfidf_cluster_indices) > 0:
                cluster_tfidf = tfidf_matrix[tfidf_cluster_indices]
                centroid = np.asarray(cluster_tfidf.mean(axis=0)).flatten()
                similarities = cosine_similarity(cluster_tfidf, centroid.reshape(1, -1)).flatten()
                most_representative_idx = similarities.argmax()
                representative_answer = cluster_data.iloc[most_representative_idx]['answer']

                cluster_analysis.append({
                    'cluster_id': cluster_id,
                    'size': len(cluster_data),
                    'representative_answer': representative_answer,
                    'part_ids': list(cluster_data['part_id'].unique()),
                    'avg_score': cluster_data['score'].mean()
                })

        return df, cluster_analysis

    except Exception as e:
        print(f"Error in semantic analysis: {e}")
        df['semantic_cluster'] = -1
        return df, []

def create_excel_report(df, part_stats, cluster_analysis, output_file):
    """Create a comprehensive Excel report"""

    # Create workbook and worksheets
    wb = openpyxl.Workbook()

    # Remove default sheet
    wb.remove(wb.active)

    # Define styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # 1. Summary Statistics Sheet
    ws_summary = wb.create_sheet("Summary Statistics")

    # Add part statistics
    for r in dataframe_to_rows(part_stats, index=False, header=True):
        ws_summary.append(r)

    # Format header
    for cell in ws_summary[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = Alignment(horizontal='center')

    # Format data cells
    for row in ws_summary.iter_rows(min_row=2):
        for cell in row:
            cell.border = border
            if isinstance(cell.value, float):
                cell.number_format = '0.00'

    # Auto-adjust column widths
    for column in ws_summary.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws_summary.column_dimensions[column_letter].width = adjusted_width

    # 2. Detailed Submissions Sheet
    ws_details = wb.create_sheet("Detailed Submissions")

    # Prepare detailed data
    detailed_data = df.copy()

    # Parse feedback for marking point evaluation
    detailed_data['marking_points'] = detailed_data['feedback'].apply(
        lambda x: parse_feedback_json(x).get('evaluated_points', [])
    )

    # Create marking point feedback column
    detailed_data['marking_point_feedback'] = detailed_data['marking_points'].apply(
        lambda points: '; '.join([f"{p.get('description', '')}: {p.get('evaluation', '')}"
                                 for p in points if isinstance(p, dict)])
    )

    # Select and order columns for Excel
    excel_columns = [
        'part_id', 'username', 'answer', 'score', 'max_score',
        'marking_point_feedback', 'timestamp', 'part_description',
        'question_description', 'topic_name', 'subject_name', 'semantic_cluster'
    ]

    detailed_excel = detailed_data[excel_columns].copy()
    detailed_excel.columns = [
        'Part ID', 'Student Answer', 'Student Answer Text', 'Score', 'Max Score',
        'Marking Point Evaluation Feedback', 'Timestamp', 'Part Description',
        'Question Description', 'Topic', 'Subject', 'Semantic Cluster'
    ]

    # Add to worksheet
    for r in dataframe_to_rows(detailed_excel, index=False, header=True):
        ws_details.append(r)

    # Format header
    for cell in ws_details[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = Alignment(horizontal='center')

    # Format data cells
    for row in ws_details.iter_rows(min_row=2):
        for cell in row:
            cell.border = border
            if cell.column == 4 or cell.column == 5:  # Score columns
                if isinstance(cell.value, (int, float)):
                    cell.number_format = '0.00'

    # Auto-adjust column widths
    for column in ws_details.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 80)
        ws_details.column_dimensions[column_letter].width = adjusted_width

    # 3. Semantic Analysis Sheet
    if cluster_analysis:
        ws_semantic = wb.create_sheet("Semantic Analysis")

        # Create cluster summary
        cluster_df = pd.DataFrame(cluster_analysis)
        cluster_df['part_ids_str'] = cluster_df['part_ids'].apply(lambda x: ', '.join(map(str, x)))

        cluster_summary = cluster_df[['cluster_id', 'size', 'representative_answer', 'part_ids_str', 'avg_score']].copy()
        cluster_summary.columns = ['Cluster ID', 'Size', 'Representative Answer', 'Part IDs', 'Average Score']

        for r in dataframe_to_rows(cluster_summary, index=False, header=True):
            ws_semantic.append(r)

        # Format header
        for cell in ws_semantic[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center')

        # Format data cells
        for row in ws_semantic.iter_rows(min_row=2):
            for cell in row:
                cell.border = border
                if cell.column == 5:  # Average score column
                    if isinstance(cell.value, (int, float)):
                        cell.number_format = '0.00'

        # Auto-adjust column widths
        for column in ws_semantic.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 100)
            ws_semantic.column_dimensions[column_letter].width = adjusted_width

    # Save workbook
    wb.save(output_file)
    print(f"Excel report saved to: {output_file}")

def main():
    """Main function"""

    # Connect to database
    conn = connect_to_database()
    if not conn:
        sys.exit(1)

    # Define date range for pilot testing (July 24-25, 2025)
    start_date = "2025-07-24"
    end_date = "2025-07-25"

    # Users to exclude
    excluded_users = ["24ywuyi736j", "24ykang458b"]

    print(f"Extracting pilot testing data for {start_date} to {end_date}")
    print(f"Excluding users: {excluded_users}")

    # Extract submissions
    df = get_submissions_for_dates(conn, start_date, end_date, excluded_users)

    if len(df) == 0:
        print("No submissions found for the specified date range.")

        # Let's check what dates we do have data for
        cursor = conn.cursor()
        cursor.execute("SELECT DATE(timestamp) as date, COUNT(*) as count FROM submissions GROUP BY DATE(timestamp) ORDER BY date DESC LIMIT 10;")
        recent_dates = cursor.fetchall()
        print("\nRecent submission dates:")
        for date, count in recent_dates:
            print(f"  {date}: {count} submissions")

        conn.close()
        return

    print(f"\n=== PILOT TESTING ANALYSIS ===")
    print(f"Total submissions: {len(df)}")
    print(f"Unique users: {df['user_id'].nunique()}")
    print(f"Unique parts: {df['part_id'].nunique()}")

    # Calculate part statistics
    print("\nCalculating part statistics...")
    part_stats = calculate_part_statistics(df)

    # Perform semantic analysis
    print("Performing semantic analysis...")
    df_with_clusters, cluster_analysis = perform_semantic_analysis(df)

    # Create Excel report
    output_file = f"pilot_testing_report_{start_date}_to_{end_date}.xlsx"
    print(f"\nCreating Excel report...")
    create_excel_report(df_with_clusters, part_stats, cluster_analysis, output_file)

    # Print summary statistics
    print(f"\n=== SUMMARY STATISTICS ===")
    print(f"Overall acceptance rate: {part_stats['acceptance_rate'].mean():.2f}%")
    print(f"Average tries to get correct: {part_stats['avg_tries_to_correct'].mean():.2f}")
    print(f"Number of semantic clusters found: {len(cluster_analysis)}")

    conn.close()

    return df_with_clusters, part_stats, cluster_analysis

if __name__ == "__main__":
    main()
