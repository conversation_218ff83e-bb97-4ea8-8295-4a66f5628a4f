#!/usr/bin/env python3
"""
Pilot Testing Data Extractor
Extracts submission data for pilot testing analysis on 24/7 and 25/7
"""

import sqlite3
import pandas as pd
import json
from datetime import datetime, date
import os
import sys

def connect_to_database():
    """Connect to the SQLite database"""
    db_paths = ['instance/database.db', 'instance/vast.db']
    
    for db_path in db_paths:
        if os.path.exists(db_path):
            print(f"Found database at: {db_path}")
            return sqlite3.connect(db_path)
    
    print("No database found!")
    return None

def get_submissions_for_dates(conn, start_date, end_date, excluded_users=None):
    """
    Extract submissions for specified date range
    
    Args:
        conn: Database connection
        start_date: Start date (YYYY-MM-DD format)
        end_date: End date (YYYY-MM-DD format)
        excluded_users: List of user IDs or usernames to exclude
    """
    
    # First, let's examine the database structure
    cursor = conn.cursor()
    
    # Get table info
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    print("Available tables:", [table[0] for table in tables])
    
    # Get submissions table structure
    cursor.execute("PRAGMA table_info(submissions);")
    columns = cursor.fetchall()
    print("\nSubmissions table structure:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
    
    # Get users table structure
    cursor.execute("PRAGMA table_info(users);")
    user_columns = cursor.fetchall()
    print("\nUsers table structure:")
    for col in user_columns:
        print(f"  {col[1]} ({col[2]})")
    
    # Query submissions for the date range
    query = """
    SELECT 
        s.id as submission_id,
        s.user_id,
        u.username,
        s.question_id,
        s.part_id,
        s.answer,
        s.score,
        s.feedback,
        s.timestamp,
        p.description as part_description,
        p.score as max_score,
        q.description as question_description
    FROM submissions s
    JOIN users u ON s.user_id = u.id
    JOIN parts p ON s.part_id = p.id
    JOIN questions q ON s.question_id = q.id
    WHERE DATE(s.timestamp) BETWEEN ? AND ?
    ORDER BY s.timestamp
    """
    
    cursor.execute(query, (start_date, end_date))
    results = cursor.fetchall()
    
    print(f"\nFound {len(results)} submissions between {start_date} and {end_date}")
    
    # Convert to DataFrame for easier analysis
    columns = [
        'submission_id', 'user_id', 'username', 'question_id', 'part_id', 
        'answer', 'score', 'feedback', 'timestamp', 'part_description', 
        'max_score', 'question_description'
    ]
    
    df = pd.DataFrame(results, columns=columns)
    
    # Filter out excluded users if specified
    if excluded_users:
        print(f"Excluding users: {excluded_users}")
        df = df[~df['username'].isin(excluded_users)]
        print(f"After exclusions: {len(df)} submissions")
    
    return df

def analyze_submissions(df):
    """Analyze the submissions data"""
    
    print("\n=== SUBMISSION ANALYSIS ===")
    
    # Basic stats
    print(f"Total submissions: {len(df)}")
    print(f"Unique users: {df['user_id'].nunique()}")
    print(f"Unique parts: {df['part_id'].nunique()}")
    
    # Date range
    df['date'] = pd.to_datetime(df['timestamp']).dt.date
    print(f"Date range: {df['date'].min()} to {df['date'].max()}")
    
    # Submissions by date
    print("\nSubmissions by date:")
    date_counts = df['date'].value_counts().sort_index()
    for date, count in date_counts.items():
        print(f"  {date}: {count} submissions")
    
    # Submissions by user
    print("\nSubmissions by user:")
    user_counts = df['username'].value_counts()
    for username, count in user_counts.items():
        print(f"  {username}: {count} submissions")
    
    # Submissions by part
    print("\nSubmissions by part:")
    part_counts = df['part_id'].value_counts()
    for part_id, count in part_counts.head(10).items():
        part_desc = df[df['part_id'] == part_id]['part_description'].iloc[0]
        print(f"  Part {part_id}: {count} submissions - {part_desc[:50]}...")
    
    return df

def main():
    """Main function"""
    
    # Connect to database
    conn = connect_to_database()
    if not conn:
        sys.exit(1)
    
    # Define date range for pilot testing
    # Assuming 24/7 and 25/7 means July 24 and 25, 2024
    start_date = "2024-07-24"
    end_date = "2024-07-25"
    
    # Users to exclude
    excluded_users = ["24ywuyi736j", "24ykang458b"]
    
    print(f"Extracting pilot testing data for {start_date} to {end_date}")
    print(f"Excluding users: {excluded_users}")
    
    # Extract submissions
    df = get_submissions_for_dates(conn, start_date, end_date, excluded_users)
    
    if len(df) == 0:
        print("No submissions found for the specified date range.")
        
        # Let's check what dates we do have data for
        cursor = conn.cursor()
        cursor.execute("SELECT DATE(timestamp) as date, COUNT(*) as count FROM submissions GROUP BY DATE(timestamp) ORDER BY date DESC LIMIT 10;")
        recent_dates = cursor.fetchall()
        print("\nRecent submission dates:")
        for date, count in recent_dates:
            print(f"  {date}: {count} submissions")
        
        conn.close()
        return
    
    # Analyze the data
    analyzed_df = analyze_submissions(df)
    
    # Save raw data to CSV for further analysis
    output_file = f"pilot_submissions_{start_date}_to_{end_date}.csv"
    analyzed_df.to_csv(output_file, index=False)
    print(f"\nRaw data saved to: {output_file}")
    
    conn.close()
    
    return analyzed_df

if __name__ == "__main__":
    main()
